import cv2
import numpy as np
from PIL import Image
import os

def remove_background(image):
    """Remove background from person image using improved GrabCut for portraits"""
    # Create a mask
    mask = np.zeros(image.shape[:2], np.uint8)
    
    # Background and foreground models
    bgd_model = np.zeros((1,65), np.float64)
    fgd_model = np.zeros((1,65), np.float64)
    
    # Define rectangle focusing on face/upper body area
    height, width = image.shape[:2]
    rect = (width//4, height//6, width//2, height//2)  # More focused on upper body
    
    # Apply GrabCut
    cv2.grabCut(image, mask, rect, bgd_model, fgd_model, 5, cv2.GC_INIT_WITH_RECT)
    
    # Create mask for probable foreground
    mask2 = np.where((mask==2)|(mask==0), 0, 1).astype('uint8')
    
    # Refine mask
    kernel = np.ones((5,5), np.uint8)
    mask2 = cv2.morphologyEx(mask2, cv2.MORPH_CLOSE, kernel)
    mask2 = cv2.morphologyEx(mask2, cv2.MORPH_OPEN, kernel)
    
    # Apply Gaussian blur for smooth edges
    mask2 = cv2.GaussianBlur(mask2, (5,5), 0)
    
    return mask2

def blend_person_into_background(person_img, background_img, person_mask):
    """Blend person into background using the mask with advanced color matching"""
    # Get image dimensions
    bg_h, bg_w = background_img.shape[:2]
    person_h, person_w = person_img.shape[:2]
    
    # Calculate scaling to fit person in background (adjust for upper body/face shot)
    scale = min(bg_h / person_h, bg_w / person_w) * 0.4  # 40% of max size for head/upper body
    new_h = int(person_h * scale)
    new_w = int(person_w * scale)
    
    # Resize person and mask with high-quality interpolation
    person_resized = cv2.resize(person_img, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
    mask_resized = cv2.resize(person_mask, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    
    # Position person in the middle-right third of the image for scenic composition
    x = bg_w // 2  # Adjust horizontal position
    y = bg_h // 3  # Place in upper third for scenic shots
    
    # Create output image
    result = background_img.copy()
    
    # Create alpha channels for blending with feathered edges
    alpha = cv2.GaussianBlur(mask_resized, (5,5), 2)
    alpha = np.repeat(alpha[:, :, np.newaxis], 3, axis=2)
    
    # Color match person to background
    roi = result[y:y+new_h, x:x+new_w]
    person_matched = person_resized.astype(float)
    
    # Match color temperature
    bg_mean = np.mean(roi, axis=(0,1))
    person_mean = np.mean(person_resized, axis=(0,1))
    color_ratio = bg_mean / (person_mean + 1e-6)
    person_matched *= color_ratio
    
    # Apply subtle shadows
    shadow = np.clip(person_matched * 0.7, 0, 255)
    
    # Blend with soft edges
    blend = roi * (1 - alpha) + person_matched.astype(np.uint8) * alpha
    
    # Apply final adjustments
    result[y:y+new_h, x:x+new_w] = cv2.addWeighted(
        roi, 0.2,  # Keep some background for transparency
        blend.astype(np.uint8), 0.8,  # Main blend
        0  # No gamma correction
    )
    
    return result

def main():
    # Create output directory
    os.makedirs('output_images', exist_ok=True)
    
    print("Starting image processing...")
    
    # Load images
    person = cv2.imread('input_images/person.jpg')
    background = cv2.imread('input_images/background.jpg')
    
    if person is None or background is None:
        print("Error: Could not load input images.")
        print("Please ensure both images exist in input_images folder:")
        print("- input_images/person.jpg")
        print("- input_images/background.jpg")
        return
    
    print("1. Removing background from person image...")
    # Remove background
    mask = remove_background(person)
    
    print("2. Blending person into new background...")
    # Blend images
    result = blend_person_into_background(person, background, mask)
    
    # Save results
    print("3. Saving results...")
    cv2.imwrite('output_images/result.jpg', result)
    cv2.imwrite('output_images/person_mask.jpg', mask * 255)  # Save mask for verification
    
    print("\nProcess completed!")
    print("Output files saved:")
    print("- output_images/result.jpg (Final result)")
    print("- output_images/person_mask.jpg (Extracted mask)")

if __name__ == "__main__":
    main()
