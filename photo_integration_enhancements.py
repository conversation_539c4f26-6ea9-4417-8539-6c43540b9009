import numpy as np
import cv2
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import logging
from tqdm import tqdm
import json
import time
from typing import Dict, Tuple, Optional
from dataclasses import dataclass
import os
from datetime import datetime
import dataclasses

@dataclass
class IntegrationMetrics:
    """Data class to store image quality metrics"""
    ssim_score: float
    psnr_score: float
    color_harmony: float
    shadow_quality: float
    edge_coherence: float
    
    def __post_init__(self):
        """Convert any numpy types to Python native types"""
        self.ssim_score = float(self.ssim_score)
        self.psnr_score = float(self.psnr_score)
        self.color_harmony = float(self.color_harmony)
        self.shadow_quality = float(self.shadow_quality)
        self.edge_coherence = float(self.edge_coherence)

class PhotoIntegrationValidator:
    """Validates and measures the quality of photo integration"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """Configure logging"""
        logger = logging.getLogger('PhotoIntegration')
        logger.setLevel(logging.INFO)
        handler = logging.FileHandler('integration_log.txt')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger

    def calculate_edge_coherence(self, image: np.ndarray, mask: np.ndarray) -> float:
        """Calculate edge coherence between person and background"""
        # Ensure mask has same dimensions as image
        mask_resized = cv2.resize(mask, (image.shape[1], image.shape[0]))
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Calculate edges
        edges = cv2.Canny(gray, 100, 200)
        
        # Calculate edge continuity around mask boundary
        dilated_mask = cv2.dilate(mask_resized, np.ones((3,3), np.uint8))
        eroded_mask = cv2.erode(mask_resized, np.ones((3,3), np.uint8))
        boundary = dilated_mask - eroded_mask
        
        edge_coherence = np.sum(edges * boundary) / np.sum(boundary)
        return float(edge_coherence)

    def measure_shadow_quality(self, image: np.ndarray, shadow_mask: np.ndarray) -> float:
        """Measure the quality of shadow integration"""
        # Ensure shadow mask has same dimensions as image
        shadow_mask_resized = cv2.resize(shadow_mask, (image.shape[1], image.shape[0]))
        
        # Convert to LAB color space
        lab_image = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        
        # Extract luminance channel
        luminance = lab_image[:,:,0]
        
        # Calculate shadow gradients
        shadow_gradients = np.gradient(luminance * shadow_mask_resized)
        smoothness = np.mean(np.abs(shadow_gradients))
        
        # Normalize score between 0 and 1
        shadow_quality = 1 - (smoothness / 255)
        return float(shadow_quality)

    def calculate_color_harmony(self, image: np.ndarray) -> float:
        """Calculate color harmony score"""
        # Convert to HSV
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        
        # Calculate hue histogram
        hue_hist = cv2.calcHist([hsv], [0], None, [180], [0,180])
        
        # Measure distribution evenness
        harmony_score = 1 - (np.std(hue_hist) / np.mean(hue_hist))
        return float(harmony_score)

    def evaluate_integration(self, 
                           result: np.ndarray, 
                           original: np.ndarray,
                           person_mask: np.ndarray,
                           shadow_mask: Optional[np.ndarray] = None) -> IntegrationMetrics:
        """Evaluate the quality of photo integration"""
        self.logger.info("Starting integration quality evaluation")
        
        # Resize original to match result dimensions for comparison
        original_resized = cv2.resize(original, (result.shape[1], result.shape[0]))
        
        metrics = IntegrationMetrics(
            ssim_score=ssim(result, original_resized, channel_axis=2),
            psnr_score=psnr(result, original_resized),
            color_harmony=self.calculate_color_harmony(result),
            edge_coherence=self.calculate_edge_coherence(result, person_mask),
            shadow_quality=self.measure_shadow_quality(result, shadow_mask) if shadow_mask is not None else 0.0
        )
        
        self.logger.info(f"Integration metrics calculated: {metrics}")
        return metrics

class ProgressTracker:
    """Track progress of integration steps"""
    
    def __init__(self):
        self.steps = [
            "Loading Images",
            "Background Removal",
            "Shadow Analysis",
            "Light Direction Estimation",
            "Color Temperature Matching",
            "Exposure Matching",
            "Color Grading",
            "Shadow Creation",
            "Final Blending",
            "Quality Validation"
        ]
        self.progress = {step: False for step in self.steps}
        
    def update_progress(self, step: str, status: bool = True):
        """Update progress for a step"""
        if step in self.progress:
            self.progress[step] = status
            self._display_progress()
            
    def _display_progress(self):
        """Display current progress"""
        completed = sum(self.progress.values())
        total = len(self.steps)
        print(f"\nProgress: {completed}/{total} steps completed")
        
        for step, status in self.progress.items():
            print(f"[{'✓' if status else ' '}] {step}")

class ResultsManager:
    """Manage and store integration results"""
    
    def __init__(self, output_dir: str = "integration_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def save_results(self, 
                    final_image: np.ndarray, 
                    metrics: IntegrationMetrics,
                    metadata: Dict):
        """Save integration results and metrics"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save final image
        cv2.imwrite(f"{self.output_dir}/result_{timestamp}.jpg", 
                    cv2.cvtColor(final_image, cv2.COLOR_RGB2BGR))
        
        # Save metrics and metadata
        results = {
            "timestamp": timestamp,
            "metrics": dataclasses.asdict(metrics),
            "metadata": metadata
        }
        
        with open(f"{self.output_dir}/results_{timestamp}.json", 'w') as f:
            json.dump(results, f, indent=4)

def main():
    # Initialize components
    validator = PhotoIntegrationValidator()
    progress = ProgressTracker()
    results_manager = ResultsManager()
    
    # Start timing
    start_time = time.time()
    
    try:
        # Your existing PhotorealisticIntegration class instance
        from photorealistic_integration import PhotorealisticIntegration
        integrator = PhotorealisticIntegration()
        
        # Process with progress tracking
        progress.update_progress("Loading Images")
        integrator.load_sample_images()
        
        progress.update_progress("Background Removal")
        person_mask = integrator.remove_background_grabcut(integrator.person_image)
        
        # Analyze shadows and lighting
        progress.update_progress("Shadow Analysis")
        shadow_analysis = integrator.detect_shadows(integrator.background_image)
        
        progress.update_progress("Light Direction Estimation")
        light_direction = integrator.estimate_light_direction(integrator.background_image, shadow_analysis)
        
        # Process color temperature and exposure
        progress.update_progress("Color Temperature Matching")
        bg_temp = integrator.analyze_color_temperature(integrator.background_image)
        person_temp = integrator.analyze_color_temperature(integrator.person_image)
        person_corrected = integrator.match_color_temperature(integrator.person_image, bg_temp, person_temp)
        
        progress.update_progress("Exposure Matching")
        person_corrected = integrator.match_exposure(person_corrected, integrator.background_image)
        
        # Apply color grading
        progress.update_progress("Color Grading")
        person_corrected = integrator.apply_color_grading(person_corrected, integrator.background_image)
        
        # Create shadow
        progress.update_progress("Shadow Creation")
        shadow_mask, shadow_color = integrator.create_shadow(person_corrected, person_mask, light_direction)
        
        # Final integration
        progress.update_progress("Final Blending")
        final_result = integrator.blend_images(
            person_corrected,
            integrator.background_image,
            person_mask,
            shadow_mask,
            shadow_color
        )
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Validate results
        progress.update_progress("Quality Validation")
        metrics = validator.evaluate_integration(
            result=final_result,
            original=integrator.background_image,
            person_mask=person_mask,
            shadow_mask=shadow_mask
        )
        
        # Save results
        # Save results with metadata
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        metadata = {
            "light_direction": {
                "azimuth": float(light_direction["azimuth"]),
                "elevation": float(light_direction["elevation"])
            },
            "color_temperature": {
                "background": float(bg_temp),
                "person": float(person_temp)
            },
            "processing_time": float(processing_time),
            "shadow_analysis": {
                "hard_shadow_pixels": int(np.sum(shadow_analysis["hard_shadows"])),
                "soft_shadow_pixels": int(np.sum(shadow_analysis["soft_shadows"])),
                "total_shadow_pixels": int(np.sum(shadow_analysis["all_shadows"]))
            }
        }
        
        results_manager.save_results(
            final_image=final_result,
            metrics=metrics,
            metadata=metadata
        )
        
        # Print summary
        print("\n✨ Integration Complete! ✨")
        print(f"Processing time: {processing_time:.2f} seconds")
        print("\nQuality Metrics:")
        print(f"  • SSIM Score: {metrics.ssim_score:.3f}")
        print(f"  • PSNR Score: {metrics.psnr_score:.2f} dB")
        print(f"  • Color Harmony: {metrics.color_harmony:.3f}")
        print(f"  • Edge Coherence: {metrics.edge_coherence:.3f}")
        print(f"  • Shadow Quality: {metrics.shadow_quality:.3f}")
        print("\nLight Direction:")
        print(f"  • Azimuth: {light_direction['azimuth']:.1f}°")
        print(f"  • Elevation: {light_direction['elevation']:.1f}°")
        print("\nColor Temperature:")
        print(f"  • Background: {bg_temp:.0f}K")
        print(f"  • Person: {person_temp:.0f}K")
        print(f"\nOutputs saved in integration_results/:")
        print(f"  • Image: result_{timestamp}.jpg")
        print(f"  • Metrics: results_{timestamp}.json")
        
    except Exception as e:
        logging.error(f"Error during integration: {str(e)}")
        raise

if __name__ == "__main__":
    main()
